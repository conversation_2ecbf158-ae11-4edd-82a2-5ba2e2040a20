/**
 * ClickHouse 数据库客户端
 */

import { createClient } from '@clickhouse/client';
import { SocksProxyAgent } from 'socks-proxy-agent';
import config from '../config/config.js';

export class ClickHouseClient {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  /**
   * 连接到 ClickHouse
   */
  async connect() {
    try {
      this.client = createClient({
        host: `http://${config.clickhouse.host}:${config.clickhouse.port}`,
        username: config.clickhouse.username,
        password: config.clickhouse.password,
        database: config.clickhouse.database,
        ...config.clickhouse.options
      });

      // 测试连接
      await this.client.ping();
      this.isConnected = true;
      console.log('ClickHouse 连接成功');
    } catch (error) {
      console.error('ClickHouse 连接失败:', error);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect() {
    if (this.client) {
      await this.client.close();
      this.isConnected = false;
      console.log('ClickHouse 连接已断开');
    }
  }

  /**
   * 创建数据库
   */
  async createDatabase() {
    try {
      const query = `CREATE DATABASE IF NOT EXISTS ${config.clickhouse.database}`;
      await this.client.command({ query });
      console.log(`数据库 ${config.clickhouse.database} 创建成功`);
    } catch (error) {
      console.error('创建数据库失败:', error);
      throw error;
    }
  }

  /**
   * 创建文件元数据表
   */
  async createMetadataTable() {
    const tableName = config.tables.metadataTable;
    const query = `
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id UUID DEFAULT generateUUIDv4(),
        file_name String,
        file_path String,
        geometry_types Array(String),
        feature_count UInt32,
        attribute_schema String,
        created_at DateTime DEFAULT now(),
        updated_at DateTime DEFAULT now()
      ) ENGINE = MergeTree()
      ORDER BY (file_name, created_at)
    `;

    try {
      await this.client.command({ query });
      console.log(`元数据表 ${tableName} 创建成功`);
    } catch (error) {
      console.error('创建元数据表失败:', error);
      throw error;
    }
  }

  /**
   * 创建几何数据表
   */
  async createGeometryTable() {
    const tableName = config.tables.geometryTable;
    const query = `
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id UUID DEFAULT generateUUIDv4(),
        file_id UUID,
        feature_id UInt32,
        geometry_type String,
        geometry_wkt String,
        geometry_geojson String,
        bbox_min_x Float64,
        bbox_min_y Float64,
        bbox_max_x Float64,
        bbox_max_y Float64,
        created_at DateTime DEFAULT now()
      ) ENGINE = MergeTree()
      ORDER BY (file_id, feature_id)
    `;

    try {
      await this.client.command({ query });
      console.log(`几何数据表 ${tableName} 创建成功`);
    } catch (error) {
      console.error('创建几何数据表失败:', error);
      throw error;
    }
  }

  /**
   * 动态创建属性数据表
   */
  async createAttributeTable(attributeSchema, tableName = null) {
    const baseTableName = tableName || config.tables.attributeTable;
    
    // 构建列定义
    const columns = ['id UUID DEFAULT generateUUIDv4()', 'file_id UUID', 'feature_id UInt32'];
    
    Object.entries(attributeSchema).forEach(([key, type]) => {
      // 清理列名，确保符合 ClickHouse 命名规范
      const cleanKey = key.replace(/[^a-zA-Z0-9_]/g, '_').toLowerCase();
      columns.push(`${cleanKey} Nullable(${type})`);
    });
    
    columns.push('created_at DateTime DEFAULT now()');

    const query = `
      CREATE TABLE IF NOT EXISTS ${baseTableName} (
        ${columns.join(',\n        ')}
      ) ENGINE = MergeTree()
      ORDER BY (file_id, feature_id)
    `;

    try {
      await this.client.command({ query });
      console.log(`属性数据表 ${baseTableName} 创建成功`);
      return baseTableName;
    } catch (error) {
      console.error('创建属性数据表失败:', error);
      throw error;
    }
  }

  /**
   * 初始化所有表
   */
  async initializeTables() {
    try {
      await this.createDatabase();
      await this.createMetadataTable();
      await this.createGeometryTable();
      console.log('所有基础表初始化完成');
    } catch (error) {
      console.error('初始化表失败:', error);
      throw error;
    }
  }

  /**
   * 执行查询
   */
  async query(sql, params = {}) {
    try {
      const result = await this.client.query({
        query: sql,
        query_params: params
      });
      return result;
    } catch (error) {
      console.error('查询执行失败:', error);
      throw error;
    }
  }

  /**
   * 批量插入数据
   */
  async insertBatch(tableName, data) {
    try {
      await this.client.insert({
        table: tableName,
        values: data,
        format: 'JSONEachRow'
      });
      console.log(`成功插入 ${data.length} 条记录到表 ${tableName}`);
    } catch (error) {
      console.error(`批量插入数据失败 (表: ${tableName}):`, error);
      throw error;
    }
  }
}

export default ClickHouseClient;
