{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../../packages/client-common/src/logger.ts"], "names": [], "mappings": ";;;AAgBA,MAAa,aAAa;IACxB,KAAK,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAa;QACxC,MAAM,MAAM,GAAc;YACxB,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;SACnD,CAAA;QACD,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QACnC,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAA;IAC1B,CAAC;IAED,KAAK,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAa;QACxC,MAAM,MAAM,GAAc;YACxB,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;SACnD,CAAA;QACD,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QACnC,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAA;IAC1B,CAAC;IAED,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAa;QACvC,MAAM,MAAM,GAAc;YACxB,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;SAClD,CAAA;QACD,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QACnC,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAA;IACzB,CAAC;IAED,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAiB;QAChD,MAAM,MAAM,GAAc;YACxB,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;SAClD,CAAA;QACD,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QACnC,CAAC;QACD,IAAI,GAAG,EAAE,CAAC;YACR,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,CAAA;QAClC,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAA;IACzB,CAAC;IAED,KAAK,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAkB;QAClD,MAAM,MAAM,GAAc;YACxB,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;SACnD,CAAA;QACD,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QACnC,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,CAAA;QAChC,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAA;IAC1B,CAAC;CACF;AAtDD,sCAsDC;AAOD,MAAa,SAAS;IAEpB,YACmB,MAAc,EACd,MAAc,EAC/B,QAA6B;QAF7B;;;;mBAAiB,MAAM;WAAQ;QAC/B;;;;mBAAiB,MAAM;WAAQ;QAHhB;;;;;WAA4B;QAM3C,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,kBAAkB,CAAC,GAAG,CAAA;QAClD,IAAI,CAAC,IAAI,CAAC;YACR,OAAO,EAAE,uBAAuB,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;SACpE,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,MAAgC;QACpC,IAAI,IAAI,CAAC,QAAQ,IAAK,kBAAkB,CAAC,KAAgB,EAAE,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAChB,GAAG,MAAM;gBACT,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;aACrC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAgC;QACpC,IAAI,IAAI,CAAC,QAAQ,IAAK,kBAAkB,CAAC,KAAgB,EAAE,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAChB,GAAG,MAAM;gBACT,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;aACrC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,IAAI,CAAC,MAA+B;QAClC,IAAI,IAAI,CAAC,QAAQ,IAAK,kBAAkB,CAAC,IAAe,EAAE,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACf,GAAG,MAAM;gBACT,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;aACrC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,IAAI,CAAC,MAA+B;QAClC,IAAI,IAAI,CAAC,QAAQ,IAAK,kBAAkB,CAAC,IAAe,EAAE,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACf,GAAG,MAAM;gBACT,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;aACrC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAgC;QACpC,IAAI,IAAI,CAAC,QAAQ,IAAK,kBAAkB,CAAC,KAAgB,EAAE,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAChB,GAAG,MAAM;gBACT,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;aACrC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;CACF;AAzDD,8BAyDC;AAED,IAAY,kBAOX;AAPD,WAAY,kBAAkB;IAC5B,6DAAS,CAAA;IACT,6DAAS,CAAA;IACT,2DAAQ,CAAA;IACR,2DAAQ,CAAA;IACR,6DAAS,CAAA;IACT,2DAAS,CAAA;AACX,CAAC,EAPW,kBAAkB,kCAAlB,kBAAkB,QAO7B;AAED,SAAS,aAAa,CAAC,EACrB,KAAK,EACL,MAAM,EACN,OAAO,GAKR;IACC,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;IACnC,OAAO,IAAI,EAAE,KAAK,KAAK,yBAAyB,MAAM,KAAK,OAAO,EAAE,CAAA;AACtE,CAAC"}