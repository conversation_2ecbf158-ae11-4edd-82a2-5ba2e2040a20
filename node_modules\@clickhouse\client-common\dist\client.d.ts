import type { BaseClickHouseClientConfigOptions, ClickHouseSettings, ConnExecResult, IsSame, WithClickHouseSummary, WithResponseHeaders } from '@clickhouse/client-common';
import { type DataFormat } from '@clickhouse/client-common';
import type { InsertValues, NonEmptyArray } from './clickhouse_types';
import type { ImplementationDetails } from './config';
import type { ConnPingResult } from './connection';
import type { BaseResultSet } from './result';
export interface BaseQueryParams {
    /** ClickHouse's settings that can be applied on query level. */
    clickhouse_settings?: ClickHouseSettings;
    /** Parameters for query binding. https://clickhouse.com/docs/en/interfaces/http/#cli-queries-with-parameters */
    query_params?: Record<string, unknown>;
    /** AbortSignal instance to cancel a request in progress. */
    abort_signal?: AbortSignal;
    /** A specific `query_id` that will be sent with this request.
     *  If it is not set, a random identifier will be generated automatically by the client. */
    query_id?: string;
    /** A specific ClickHouse Session id for this query.
     *  If it is not set, {@link BaseClickHouseClientConfigOptions.session_id} will be used.
     *  @default undefined (no override) */
    session_id?: string;
    /** A specific list of roles to use for this query.
     *  If it is not set, {@link BaseClickHouseClientConfigOptions.role} will be used.
     *  @default undefined (no override) */
    role?: string | Array<string>;
    /** When defined, overrides {@link BaseClickHouseClientConfigOptions.auth} for this particular request.
     *  @default undefined (no override) */
    auth?: {
        username: string;
        password: string;
    } | {
        access_token: string;
    };
    /** Additional HTTP headers to attach to this particular request.
     *  Overrides the headers set in {@link BaseClickHouseClientConfigOptions.http_headers}.
     *  @default empty object */
    http_headers?: Record<string, string>;
}
export interface QueryParams extends BaseQueryParams {
    /** Statement to execute. */
    query: string;
    /** Format of the resulting dataset. */
    format?: DataFormat;
}
/** Same parameters as {@link QueryParams}, but with `format` field as a type */
export type QueryParamsWithFormat<Format extends DataFormat> = Omit<QueryParams, 'format'> & {
    format?: Format;
};
/** If the Format is not a literal type, fall back to the default behavior of the ResultSet,
 *  allowing to call all methods with all data shapes variants,
 *  and avoiding generated types that include all possible DataFormat literal values. */
export type QueryResult<Stream, Format extends DataFormat> = IsSame<Format, DataFormat> extends true ? BaseResultSet<Stream, unknown> : BaseResultSet<Stream, Format>;
export type ExecParams = BaseQueryParams & {
    /** Statement to execute (including the FORMAT clause). By default, the query will be sent in the request body;
     *  If {@link ExecParamsWithValues.values} are defined, the query is sent as a request parameter,
     *  and the values are sent in the request body instead. */
    query: string;
    /** If set to `false`, the client _will not_ decompress the response stream, even if the response compression
     *  was requested by the client via the {@link BaseClickHouseClientConfigOptions.compression.response } setting.
     *  This could be useful if the response stream is passed to another application as-is,
     *  and the decompression is handled there.
     *  @note 1) Node.js only. This setting will have no effect on the Web version.
     *  @note 2) In case of an error, the stream will be decompressed anyway, regardless of this setting.
     *  @default true */
    decompress_response_stream?: boolean;
};
export type ExecParamsWithValues<Stream> = ExecParams & {
    /** If you have a custom INSERT statement to run with `exec`, the data from this stream will be inserted.
     *
     *  NB: the data in the stream is expected to be serialized accordingly to the FORMAT clause
     *  used in {@link ExecParams.query} in this case.
     *
     *  @see https://clickhouse.com/docs/en/interfaces/formats */
    values: Stream;
};
export type CommandParams = ExecParams;
export type CommandResult = {
    query_id: string;
} & WithClickHouseSummary & WithResponseHeaders;
export type InsertResult = {
    /**
     * Indicates whether the INSERT statement was executed on the server.
     * Will be `false` if there was no data to insert.
     * For example, if {@link InsertParams.values} was an empty array,
     * the client does not send any requests to the server, and {@link executed} is false.
     */
    executed: boolean;
    /**
     * Empty string if {@link executed} is false.
     * Otherwise, either {@link InsertParams.query_id} if it was set, or the id that was generated by the client.
     */
    query_id: string;
} & WithClickHouseSummary & WithResponseHeaders;
export type ExecResult<Stream> = ConnExecResult<Stream>;
/** {@link except} field contains a non-empty list of columns to exclude when generating `(* EXCEPT (...))` clause */
export interface InsertColumnsExcept {
    except: NonEmptyArray<string>;
}
export interface InsertParams<Stream = unknown, T = unknown> extends BaseQueryParams {
    /** Name of a table to insert into. */
    table: string;
    /** A dataset to insert. */
    values: InsertValues<Stream, T>;
    /** Format of the dataset to insert. Default: `JSONCompactEachRow` */
    format?: DataFormat;
    /**
     * Allows specifying which columns the data will be inserted into.
     * Accepts either an array of strings (column names) or an object of {@link InsertColumnsExcept} type.
     * Examples of generated queries:
     *
     * - An array such as `['a', 'b']` will generate: `INSERT INTO table (a, b) FORMAT DataFormat`
     * - An object such as `{ except: ['a', 'b'] }` will generate: `INSERT INTO table (* EXCEPT (a, b)) FORMAT DataFormat`
     *
     * By default, the data is inserted into all columns of the {@link InsertParams.table},
     * and the generated statement will be: `INSERT INTO table FORMAT DataFormat`.
     *
     * See also: https://clickhouse.com/docs/en/sql-reference/statements/insert-into */
    columns?: NonEmptyArray<string> | InsertColumnsExcept;
}
/** Parameters for the health-check request - using the built-in `/ping` endpoint.
 *  This is the default behavior for the Node.js version. */
export type PingParamsWithEndpoint = {
    select: false;
} & Pick<BaseQueryParams, 'abort_signal' | 'http_headers'>;
/** Parameters for the health-check request - using a SELECT query.
 *  This is the default behavior for the Web version, as the `/ping` endpoint does not support CORS.
 *  Most of the standard `query` method params, e.g., `query_id`, `abort_signal`, `http_headers`, etc. will work,
 *  except for `query_params`, which does not make sense to allow in this method. */
export type PingParamsWithSelectQuery = {
    select: true;
} & Omit<BaseQueryParams, 'query_params'>;
export type PingParams = PingParamsWithEndpoint | PingParamsWithSelectQuery;
export type PingResult = ConnPingResult;
export declare class ClickHouseClient<Stream = unknown> {
    private readonly clientClickHouseSettings;
    private readonly connectionParams;
    private readonly connection;
    private readonly makeResultSet;
    private readonly valuesEncoder;
    private readonly sessionId?;
    private readonly role?;
    private readonly logWriter;
    constructor(config: BaseClickHouseClientConfigOptions & ImplementationDetails<Stream>);
    /**
     * Used for most statements that can have a response, such as `SELECT`.
     * FORMAT clause should be specified separately via {@link QueryParams.format} (default is `JSON`).
     * Consider using {@link ClickHouseClient.insert} for data insertion, or {@link ClickHouseClient.command} for DDLs.
     * Returns an implementation of {@link BaseResultSet}.
     *
     * See {@link DataFormat} for the formats supported by the client.
     */
    query<Format extends DataFormat = 'JSON'>(params: QueryParamsWithFormat<Format>): Promise<QueryResult<Stream, Format>>;
    /**
     * It should be used for statements that do not have any output,
     * when the format clause is not applicable, or when you are not interested in the response at all.
     * The response stream is destroyed immediately as we do not expect useful information there.
     * Examples of such statements are DDLs or custom inserts.
     *
     * @note if you have a custom query that does not work with {@link ClickHouseClient.query},
     * and you are interested in the response data, consider using {@link ClickHouseClient.exec}.
     */
    command(params: CommandParams): Promise<CommandResult>;
    /**
     * Similar to {@link ClickHouseClient.command}, but for the cases where the output _is expected_,
     * but format clause is not applicable. The caller of this method _must_ consume the stream,
     * as the underlying socket will not be released until then, and the request will eventually be timed out.
     *
     * @note it is not intended to use this method to execute the DDLs, such as `CREATE TABLE` or similar;
     * use {@link ClickHouseClient.command} instead.
     */
    exec(params: ExecParams | ExecParamsWithValues<Stream>): Promise<ExecResult<Stream>>;
    /**
     * The primary method for data insertion. It is recommended to avoid arrays in case of large inserts
     * to reduce application memory consumption and consider streaming for most of such use cases.
     * As the insert operation does not provide any output, the response stream is immediately destroyed.
     *
     * @note in case of a custom insert operation (e.g., `INSERT FROM SELECT`),
     * consider using {@link ClickHouseClient.command}, passing the entire raw query there
     * (including the `FORMAT` clause).
     */
    insert<T>(params: InsertParams<Stream, T>): Promise<InsertResult>;
    /**
     * A health-check request. It does not throw if an error occurs - the error is returned inside the result object.
     *
     * By default, Node.js version uses the built-in `/ping` endpoint, which does not verify credentials.
     * Optionally, it can be switched to a `SELECT` query (see {@link PingParamsWithSelectQuery}).
     * In that case, the server will verify the credentials.
     *
     * **NOTE**: Since the `/ping` endpoint does not support CORS, the Web version always uses a `SELECT` query.
     */
    ping(params?: PingParams): Promise<PingResult>;
    /**
     * Shuts down the underlying connection.
     * This method should ideally be called only once per application lifecycle,
     * for example, during the graceful shutdown phase.
     */
    close(): Promise<void>;
    private withClientQueryParams;
}
