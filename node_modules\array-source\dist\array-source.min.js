// https://github.com/mbostock/array-source Version 0.0.4. Copyright 2017 <PERSON>.
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(e.sources=e.sources||{},e.sources.array=r())}(this,function(){"use strict";function e(e){this._array=e}return e.prototype.read=function(){var e=this._array;return this._array=null,Promise.resolve(e?{done:!1,value:e}:{done:!0,value:void 0})},e.prototype.cancel=function(){return this._array=null,Promise.resolve()},function(r){return new e(r instanceof Uint8Array?r:new Uint8Array(r))}});