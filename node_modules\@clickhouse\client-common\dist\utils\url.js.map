{"version": 3, "file": "url.js", "sourceRoot": "", "sources": ["../../../../packages/client-common/src/utils/url.ts"], "names": [], "mappings": ";;AAGA,oCA2BC;AAcD,wCAkDC;AA9FD,sDAA0E;AAG1E,SAAgB,YAAY,CAAC,EAC3B,GAAG,EACH,QAAQ,EACR,YAAY,GAKb;IACC,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAA;IAE3B,IAAI,QAAQ,EAAE,CAAC;QACb,oEAAoE;QACpE,wEAAwE;QACxE,2DAA2D;QAC3D,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;YAC5B,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAC5B,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAA;QAC7B,CAAC;IACH,CAAC;IAED,IAAI,YAAY,EAAE,CAAC;QACjB,MAAM,CAAC,MAAM,GAAG,YAAY,EAAE,QAAQ,EAAE,CAAA;IAC1C,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAYD,kDAAkD;AAClD,mGAAmG;AACnG,SAAgB,cAAc,CAAC,EAC7B,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,mBAAmB,EACnB,UAAU,EACV,QAAQ,EACR,IAAI,GACkB;IACtB,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAA;IACpC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;IAEhC,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;QAC/B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACxD,MAAM,cAAc,GAAG,IAAA,kCAAiB,EAAC,EAAE,KAAK,EAAE,CAAC,CAAA;YACnD,MAAM,CAAC,GAAG,CAAC,SAAS,GAAG,EAAE,EAAE,cAAc,CAAC,CAAA;QAC5C,CAAC;IACH,CAAC;IAED,IAAI,mBAAmB,KAAK,SAAS,EAAE,CAAC;QACtC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC/D,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAA,oCAAmB,EAAC,KAAK,CAAC,CAAC,CAAA;YAC7C,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QACrD,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;IAClC,CAAC;IAED,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED,IAAI,UAAU,EAAE,CAAC;QACf,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;IACtC,CAAC;IAED,IAAI,IAAI,EAAE,CAAC;QACT,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC1B,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBACrB,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC"}