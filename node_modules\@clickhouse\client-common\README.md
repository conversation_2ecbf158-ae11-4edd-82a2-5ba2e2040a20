<p align="center">
<img src=".static/logo.svg" width="200px" align="center">
<h1 align="center">ClickHouse JS client</h1>
</p>
<br/>
<p align="center">
<a href="https://www.npmjs.com/package/@clickhouse/client">
<img alt="NPM Version" src="https://img.shields.io/npm/v/%40clickhouse%2Fclient?color=%233178C6&logo=npm">
</a>

<a href="https://www.npmjs.com/package/@clickhouse/client">
<img alt="NPM Downloads" src="https://img.shields.io/npm/dw/%40clickhouse%2Fclient?color=%233178C6&logo=npm">
</a>

<a href="https://github.com/ClickHouse/clickhouse-js/actions/workflows/tests.yml">
<img src="https://github.com/ClickHouse/clickhouse-js/actions/workflows/tests.yml/badge.svg?branch=main">
</a>

<a href="https://codecov.io/gh/ClickHouse/clickhouse-js">
<img src="https://codecov.io/gh/ClickHouse/clickhouse-js/graph/badge.svg?token=B832WB00WJ">
</a>

<img src="https://api.scorecard.dev/projects/github.com/ClickHouse/clickhouse-js/badge">
</p>

## About

Official JS client for [ClickHouse](https://clickhouse.com/), written purely in TypeScript, thoroughly tested with actual ClickHouse versions.

The repository consists of three packages:

- `@clickhouse/client` - a version of the client designed for Node.js platform only. It is built on top of [HTTP](https://nodejs.org/api/http.html)
  and [Stream](https://nodejs.org/api/stream.html) APIs; supports streaming for both selects and inserts.
- `@clickhouse/client-web` - a version of the client built on top of [Fetch](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)
  and [Web Streams](https://developer.mozilla.org/en-US/docs/Web/API/Streams_API) APIs; supports streaming for selects.
  Compatible with Chrome/Firefox browsers and CloudFlare workers.
- `@clickhouse/client-common` - shared common types and the base framework for building a custom client implementation.

## Documentation

See the [ClickHouse website](https://clickhouse.com/docs/en/integrations/language-clients/javascript) for the full documentation entry.

## Usage examples

We have a wide range of [examples](./examples), aiming to cover various scenarios of client usage. The overview is available in the [examples README](https://github.com/ClickHouse/clickhouse-js/blob/main/examples/README.md#overview).

## Contact us

If you have any questions or need help, feel free to reach out to us in the [Community Slack](https://clickhouse.com/slack) (`#clickhouse-js` channel) or via [GitHub issues](https://github.com/ClickHouse/clickhouse-js/issues).

## Contributing

Check out our [contributing guide](./CONTRIBUTING.md).
