{"name": "stream-source", "version": "0.3.5", "description": "Read binary streams in chunks, on demand, with promises.", "keywords": ["binary", "stream", "reader"], "homepage": "https://github.com/mbostock/stream-source", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "unpkg": "dist/stream-source.js", "jsdelivr": "dist/stream-source.js", "main": "index.node.js", "module": "index.js", "repository": {"type": "git", "url": "http://github.com/mbostock/stream-source.git"}, "scripts": {"prepublishOnly": "rm -rf dist && mkdir dist && rollup -c --banner \"$(preamble)\" && uglifyjs -b beautify=false,preamble=\"'$(preamble)'\" -o dist/stream-source.min.js -c -m -- dist/stream-source.js", "postpublish": "git push && git push --tags"}, "devDependencies": {"package-preamble": "0.1", "rollup": "0.49", "uglify-js": "3"}}