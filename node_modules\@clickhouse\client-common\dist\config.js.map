{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../../packages/client-common/src/config.ts"], "names": [], "mappings": ";;AA2KA,oDAoCC;AAED,kDAmCC;AAOD,oCAqCC;AAED,8BAoBC;AAOD,4DAuIC;AAED,sDAaC;AAED,oDAwBC;AAED,gDAoBC;AA/fD,qCAAwD;AA+JxD;;;;;;;GAOG;AACH,SAAgB,oBAAoB,CAClC,iBAAoD,EACpD,MAAc,EACd,mBAAuD;IAEvD,MAAM,UAAU,GAAG,EAAE,GAAG,iBAAiB,EAAE,CAAA;IAC3C,IAAI,UAAU,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;QAChD,MAAM,CAAC,IAAI,CAAC;YACV,MAAM,EAAE,QAAQ;YAChB,OAAO,EACL,iEAAiE;SACpE,CAAC,CAAA;QACF,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,kBAAkB,CAAA;QACvD,OAAO,UAAU,CAAC,kBAAkB,CAAA;IACtC,CAAC;IACD,IAAI,SAAS,CAAA;IACb,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAClC,MAAM,CAAC,IAAI,CAAC;YACV,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAE,0CAA0C;SACpD,CAAC,CAAA;QACF,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACtC,OAAO,UAAU,CAAC,IAAI,CAAA;IACxB,CAAC;SAAM,CAAC;QACN,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;IACvC,CAAC;IACD,MAAM,CAAC,GAAG,EAAE,aAAa,CAAC,GAAG,wBAAwB,CACnD,SAAS,EACT,mBAAmB,CACpB,CAAA;IACD,MAAM,MAAM,GAAG,YAAY,CAAC,UAAU,EAAE,aAAa,EAAE,MAAM,CAAC,CAAA;IAC9D,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;QAClC,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAA;IAChC,CAAC;IACD,MAAM,CAAC,GAAG,GAAG,GAAG,CAAA;IAChB,OAAO,MAAkD,CAAA;AAC3D,CAAC;AAED,SAAgB,mBAAmB,CACjC,MAAgD,EAChD,MAAc;IAEd,IAAI,IAA8B,CAAA;IAClC,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;QACtC,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CACb,uHAAuH,CACxH,CAAA;QACH,CAAC;QACD,IAAI,GAAG,EAAE,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;IAC3D,CAAC;SAAM,CAAC;QACN,IAAI,GAAG;YACL,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,SAAS;YACtC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;YAC/B,IAAI,EAAE,aAAa;SACpB,CAAA;IACH,CAAC;IACD,OAAO;QACL,IAAI;QACJ,GAAG,EAAE,MAAM,CAAC,GAAG;QACf,cAAc,EAAE,MAAM,CAAC,WAAW;QAClC,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,KAAM;QACjD,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,IAAI,EAAE;QACvD,WAAW,EAAE;YACX,mBAAmB,EAAE,MAAM,CAAC,WAAW,EAAE,QAAQ,IAAI,KAAK;YAC1D,gBAAgB,EAAE,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,KAAK;SACvD;QACD,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,SAAS;QACtC,UAAU,EAAE,IAAI,kBAAS,CAAC,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC;QAClE,UAAU,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,UAAU,EAAE,OAAO,IAAI,IAAI,EAAE;QAC3D,mBAAmB,EAAE,MAAM,CAAC,mBAAmB,IAAI,EAAE;QACrD,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;KACxC,CAAA;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,YAAY,CAC1B,UAA6C,EAC7C,aAAgD,EAChD,MAAc;IAEd,SAAS,SAAS,CAChB,IAAyB,EACzB,OAA4B,EAC5B,OAAiB,EAAE;QAEnB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACrC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;YACjD,CAAC;iBAAM,CAAC;gBACN,IAAI,UAAU,GAAwB,IAAI,CAAA;gBAC1C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;oBACvB,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;wBAClC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;oBACtB,CAAC;oBACD,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;gBAC9B,CAAC;gBACD,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;gBACjC,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;oBAC3C,MAAM,CAAC,IAAI,CAAC;wBACV,MAAM,EAAE,QAAQ;wBAChB,OAAO,EAAE,IAAI,QAAQ,qCAAqC;qBAC3D,CAAC,CAAA;gBACJ,CAAC;gBACD,UAAU,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,MAAM,GAAwB,EAAE,GAAG,UAAU,EAAE,CAAA;IACrD,SAAS,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;IAChC,OAAO,MAA2C,CAAA;AACpD,CAAC;AAED,SAAgB,SAAS,CAAC,SAAmC;IAC3D,IAAI,GAAQ,CAAA;IACZ,IAAI,CAAC;QACH,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,YAAY,GAAG,EAAE,CAAC;YAC9D,GAAG,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAA;QAC1B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,GAAG,CAAC,uBAAuB,CAAC,CAAA;QACzC,CAAC;IACH,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CACb,oIAAoI,EACpI,EAAE,KAAK,EAAE,GAAG,EAAE,CACf,CAAA;IACH,CAAC;IACD,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,IAAI,GAAG,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAC1D,MAAM,IAAI,KAAK,CACb,8DAA8D,GAAG,CAAC,QAAQ,EAAE,CAC7E,CAAA;IACH,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC;AAED;;;;GAIG;AACH,SAAgB,wBAAwB,CACtC,GAAQ,EACR,oBAAwD;IAExD,IAAI,MAAM,GAAsC,EAAE,CAAA;IAClD,oGAAoG;IACpG,IAAI,GAAG,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;QACxB,MAAM,CAAC,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IACpD,CAAC;IACD,IAAI,GAAG,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;QACxB,MAAM,CAAC,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IACpD,CAAC;IACD,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACnC,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACzC,CAAC;IACD,MAAM,mBAAmB,GAAG,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAA;IACxD,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACnC,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAA;QACvC,MAAM,aAAa,GAAG,qBAAqB,CAAA;QAC3C,MAAM,kBAAkB,GAAG,KAAK,CAAA;QAChC,MAAM,gBAAgB,GAAG,cAAc,CAAA;QACvC,mBAAmB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAClC,IAAI,iBAAiB,GAAG,IAAI,CAAA;YAC5B,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAW,CAAA;YACjD,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBAClC,wBAAwB;gBACxB,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;gBAClD,IAAI,MAAM,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;oBAC7C,MAAM,CAAC,mBAAmB,GAAG,EAAE,CAAA;gBACjC,CAAC;gBACD,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,GAAG,KAAK,CAAA;YAChD,CAAC;iBAAM,IAAI,GAAG,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC9C,OAAO;gBACP,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;gBACvD,IAAI,MAAM,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;oBAC7C,MAAM,CAAC,mBAAmB,GAAG,EAAE,CAAA;gBACjC,CAAC;gBACD,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,GAAG,KAAK,CAAA;YAChD,CAAC;iBAAM,IAAI,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC5C,iBAAiB;gBACjB,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;gBACpD,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;oBACtC,MAAM,CAAC,YAAY,GAAG,EAAE,CAAA;gBAC1B,CAAC;gBACD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;YACxC,CAAC;iBAAM,CAAC;gBACN,0BAA0B;gBAC1B,QAAQ,GAAG,EAAE,CAAC;oBACZ,KAAK,aAAa;wBAChB,MAAM,CAAC,WAAW,GAAG,KAAK,CAAA;wBAC1B,MAAK;oBACP,KAAK,UAAU;wBACb,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAA;wBACvB,MAAK;oBACP,KAAK,YAAY;wBACf,MAAM,CAAC,UAAU,GAAG,KAAK,CAAA;wBACzB,MAAK;oBACP,KAAK,iBAAiB;wBACpB,MAAM,CAAC,eAAe,GAAG,oBAAoB,CAAC;4BAC5C,GAAG;4BACH,KAAK;4BACL,GAAG,EAAE,CAAC;yBACP,CAAC,CAAA;wBACF,MAAK;oBACP,KAAK,sBAAsB;wBACzB,MAAM,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;4BACjD,GAAG;4BACH,KAAK;4BACL,GAAG,EAAE,CAAC;yBACP,CAAC,CAAA;wBACF,MAAK;oBACP,KAAK,qBAAqB;wBACxB,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;4BACrC,MAAM,CAAC,WAAW,GAAG,EAAE,CAAA;wBACzB,CAAC;wBACD,MAAM,CAAC,WAAW,CAAC,OAAO,GAAG,qBAAqB,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAA;wBAClE,MAAK;oBACP,KAAK,sBAAsB;wBACzB,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;4BACrC,MAAM,CAAC,WAAW,GAAG,EAAE,CAAA;wBACzB,CAAC;wBACD,MAAM,CAAC,WAAW,CAAC,QAAQ,GAAG,qBAAqB,CAAC;4BAClD,GAAG;4BACH,KAAK;yBACN,CAAC,CAAA;wBACF,MAAK;oBACP,KAAK,WAAW;wBACd,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;4BAC7B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAA;wBACjB,CAAC;wBACD,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,kBAAkB,CAAC;4BACpC,GAAG;4BACH,KAAK;4BACL,UAAU,EAAE,2BAAkB;yBAC/B,CAAC,CAAA;wBACF,MAAK;oBACP,KAAK,oBAAoB;wBACvB,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;4BACpC,MAAM,CAAC,UAAU,GAAG,EAAE,CAAA;wBACxB,CAAC;wBACD,MAAM,CAAC,UAAU,CAAC,OAAO,GAAG,qBAAqB,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAA;wBACjE,MAAK;oBACP,KAAK,cAAc;wBACjB,MAAM,CAAC,YAAY,GAAG,KAAK,CAAA;wBAC3B,MAAK;oBACP;wBACE,iBAAiB,GAAG,KAAK,CAAA;wBACzB,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;wBACtB,MAAK;gBACT,CAAC;YACH,CAAC;YACD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,uDAAuD;gBACvD,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAC9B,CAAC;QACH,CAAC,CAAC,CAAA;QACF,IAAI,oBAAoB,KAAK,IAAI,EAAE,CAAC;YAClC,MAAM,GAAG,GAAG,oBAAoB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;YAC7C,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;YACnB,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC3B,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;YAC5D,CAAC;YACD,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAChC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACzD,CAAC;QACH,CAAC;QACD,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CACb,2BAA2B,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAClE,CAAA;QACH,CAAC;IACH,CAAC;IACD,iEAAiE;IACjE,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;IAC7D,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA;AAChC,CAAC;AAED,SAAgB,qBAAqB,CAAC,EACpC,GAAG,EACH,KAAK,GAIN;IACC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAA;IAC5B,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,GAAG;QAAE,OAAO,IAAI,CAAA;IACtD,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,KAAK,GAAG;QAAE,OAAO,KAAK,CAAA;IACxD,MAAM,IAAI,KAAK,CACb,IAAI,GAAG,gCAAgC,OAAO,uCAAuC,CACtF,CAAA;AACH,CAAC;AAED,SAAgB,oBAAoB,CAAC,EACnC,GAAG,EACH,KAAK,EACL,GAAG,EACH,GAAG,GAMJ;IACC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAA;IAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;IAC9B,IAAI,KAAK,CAAC,MAAM,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,IAAI,GAAG,gCAAgC,OAAO,EAAE,CAAC,CAAA;IACnE,IAAI,GAAG,KAAK,SAAS,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,IAAI,GAAG,WAAW,OAAO,6BAA6B,GAAG,EAAE,CAAC,CAAA;IAC9E,CAAC;IACD,IAAI,GAAG,KAAK,SAAS,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CACb,IAAI,GAAG,WAAW,OAAO,gCAAgC,GAAG,EAAE,CAC/D,CAAA;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAgB,kBAAkB,CAA2B,EAC3D,GAAG,EACH,KAAK,EACL,UAAU,GAOX;IACC,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC5E,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAA;IAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC9B,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClC,MAAM,IAAI,KAAK,CACb,IAAI,GAAG,wBAAwB,OAAO,sBAAsB,QAAQ,GAAG,CACxE,CAAA;IACH,CAAC;IACD,OAAO,UAAU,CAAC,OAAc,CAAC,CAAA;AACnC,CAAC"}