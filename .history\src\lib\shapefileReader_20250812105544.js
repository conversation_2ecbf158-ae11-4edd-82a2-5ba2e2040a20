/**
 * SHP 和 DBF 文件读取模块
 */

import shapefile from 'shapefile';
import fs from 'fs-extra';
import path from 'path';
import { glob } from 'glob';

export class ShapefileReader {
  constructor(sourceDirectory) {
    this.sourceDirectory = sourceDirectory;
  }

  /**
   * 获取目录下所有的 SHP 文件
   */
  async getShapefiles() {
    try {
      const shpPattern = path.join(this.sourceDirectory, '**/*.shp');
      const shpFiles = await glob(shpPattern, { windowsPathsNoEscape: true });
      
      console.log(`找到 ${shpFiles.length} 个 SHP 文件`);
      return shpFiles;
    } catch (error) {
      console.error('获取 SHP 文件列表失败:', error);
      throw error;
    }
  }

  /**
   * 读取单个 SHP 文件及其对应的 DBF 文件
   */
  async readShapefile(shpFilePath) {
    try {
      console.log(`正在读取文件: ${shpFilePath}`);
      
      // 检查文件是否存在
      if (!await fs.pathExists(shpFilePath)) {
        throw new Error(`SHP 文件不存在: ${shpFilePath}`);
      }

      // 获取对应的 DBF 文件路径
      const dbfFilePath = shpFilePath.replace('.shp', '.dbf');
      if (!await fs.pathExists(dbfFilePath)) {
        console.warn(`DBF 文件不存在: ${dbfFilePath}`);
      }

      // 读取 shapefile
      const features = [];
      const source = await shapefile.open(shpFilePath, dbfFilePath);
      
      let result = await source.read();
      while (!result.done) {
        if (result.value) {
          features.push(result.value);
        }
        result = await source.read();
      }

      console.log(`成功读取 ${features.length} 个要素`);
      
      return {
        filePath: shpFilePath,
        fileName: path.basename(shpFilePath, '.shp'),
        features: features,
        featureCount: features.length
      };
    } catch (error) {
      console.error(`读取 SHP 文件失败 ${shpFilePath}:`, error);
      throw error;
    }
  }

  /**
   * 读取所有 SHP 文件
   */
  async readAllShapefiles() {
    try {
      const shpFiles = await this.getShapefiles();
      const results = [];

      for (const shpFile of shpFiles) {
        try {
          const data = await this.readShapefile(shpFile);
          results.push(data);
        } catch (error) {
          console.error(`跳过文件 ${shpFile}:`, error.message);
          continue;
        }
      }

      return results;
    } catch (error) {
      console.error('读取所有 SHP 文件失败:', error);
      throw error;
    }
  }

  /**
   * 分析要素的几何类型和属性结构
   */
  analyzeFeatures(features) {
    if (!features || features.length === 0) {
      return {
        geometryTypes: [],
        attributeSchema: {},
        sampleFeature: null
      };
    }

    const geometryTypes = new Set();
    const attributeSchema = {};
    const sampleFeature = features[0];

    // 分析几何类型
    features.forEach(feature => {
      if (feature.geometry && feature.geometry.type) {
        geometryTypes.add(feature.geometry.type);
      }
    });

    // 分析属性结构
    if (sampleFeature && sampleFeature.properties) {
      Object.keys(sampleFeature.properties).forEach(key => {
        const value = sampleFeature.properties[key];
        let type = 'String';
        
        if (typeof value === 'number') {
          type = Number.isInteger(value) ? 'Int64' : 'Float64';
        } else if (typeof value === 'boolean') {
          type = 'UInt8';
        } else if (value instanceof Date) {
          type = 'DateTime';
        }
        
        attributeSchema[key] = type;
      });
    }

    return {
      geometryTypes: Array.from(geometryTypes),
      attributeSchema,
      sampleFeature
    };
  }
}

export default ShapefileReader;
