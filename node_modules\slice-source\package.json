{"name": "slice-source", "version": "0.4.1", "description": "A readable stream reader that reads a desired number of bytes.", "keywords": ["binary", "stream", "reader"], "homepage": "https://github.com/mbostock/slice-source", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "main": "dist/slice-source.js", "module": "index.js", "repository": {"type": "git", "url": "http://github.com/mbostock/slice-source.git"}, "scripts": {"prepublish": "rm -rf dist && mkdir dist && rollup --banner \"$(preamble)\" -f umd -n sources.slice -o dist/slice-source.js -- index.js && uglifyjs --preamble \"$(preamble)\" -o dist/slice-source.min.js -cm -- dist/slice-source.js", "postpublish": "git push && git push --tags"}, "devDependencies": {"package-preamble": "0.0", "rollup": "0.34", "uglify-js": "2"}}