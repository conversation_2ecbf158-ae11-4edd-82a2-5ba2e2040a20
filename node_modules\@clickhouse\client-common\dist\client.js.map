{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../packages/client-common/src/client.ts"], "names": [], "mappings": ";;;AAYA,6DAA0E;AAG1E,qCAAoE;AAoJpE,MAAa,gBAAgB;IAU3B,YACE,MAAyE;QAV1D;;;;;WAA4C;QAC5C;;;;;WAAkC;QAClC;;;;;WAA8B;QAC9B;;;;;WAAoC;QACpC;;;;;WAAoC;QACpC;;;;;WAAkB;QAClB;;;;;WAA6B;QAC7B;;;;;WAAoB;QAKnC,MAAM,MAAM,GAAG,MAAM,EAAE,GAAG,EAAE,WAAW;YACrC,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE;YAC9B,CAAC,CAAC,IAAI,6BAAa,EAAE,CAAA;QACvB,MAAM,aAAa,GAAG,IAAA,6BAAoB,EACxC,MAAM,EACN,MAAM,EACN,MAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAC/C,CAAA;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAA,4BAAmB,EAAC,aAAa,EAAE,MAAM,CAAC,CAAA;QAClE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAA;QACjD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAA;QACzE,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,UAAU,CAAA;QAClC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QACvB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAC3C,aAAa,EACb,IAAI,CAAC,gBAAgB,CACtB,CAAA;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAA;QAChD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAA;IACjD,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,KAAK,CACT,MAAqC;QAErC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAA;QACtC,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAA;QACtD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;YACzE,KAAK;YACL,GAAG,WAAW;SACf,CAAC,CAAA;QACF,OAAO,IAAI,CAAC,aAAa,CACvB,MAAM,EACN,MAAM,EACN,QAAQ,EACR,CAAC,GAAG,EAAE,EAAE;YACN,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;gBACnB,GAAG;gBACH,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,uCAAuC;gBAChD,IAAI,EAAE;oBACJ,UAAU,EAAE,WAAW,CAAC,UAAU;oBAClC,IAAI,EAAE,WAAW,CAAC,IAAI;oBACtB,KAAK;oBACL,QAAQ;iBACT;aACF,CAAC,CAAA;QACJ,CAAC,EACD,gBAAgB,CACjB,CAAA;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,OAAO,CAAC,MAAqB;QACjC,MAAM,KAAK,GAAG,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAA;QACrD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,KAAK;YACL,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;SACtC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,IAAI,CACR,MAAiD;QAEjD,MAAM,KAAK,GAAG,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAA;QACrD,MAAM,MAAM,GAAG,QAAQ,IAAI,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAA;QAC7D,MAAM,0BAA0B,GAAG,MAAM,CAAC,0BAA0B,IAAI,IAAI,CAAA;QAC5E,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YAChC,KAAK;YACL,MAAM;YACN,0BAA0B;YAC1B,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;SACtC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,MAAM,CAAI,MAA+B;QAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/D,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAA;QAChE,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,oBAAoB,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAE9D,MAAM,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAC5C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YAC1C,KAAK;YACL,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;YAC9D,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;SACtC,CAAC,CAAA;QACF,OAAO,EAAE,GAAG,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAA;IACtC,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,IAAI,CAAC,MAAmB;QAC5B,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAK;QACT,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAA;IACtC,CAAC;IAEO,qBAAqB,CAAC,MAAuB;QACnD,OAAO;YACL,mBAAmB,EAAE;gBACnB,GAAG,IAAI,CAAC,wBAAwB;gBAChC,GAAG,MAAM,CAAC,mBAAmB;aAC9B;YACD,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS;YAC/C,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;YAC9B,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,YAAY,EAAE,MAAM,CAAC,YAAY;SAClC,CAAA;IACH,CAAC;CACF;AA/KD,4CA+KC;AAED,SAAS,WAAW,CAAC,KAAa,EAAE,MAAkB;IACpD,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAA;IACpB,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAA;IACjC,OAAO,KAAK,GAAG,YAAY,GAAG,MAAM,CAAA;AACtC,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAa;IACvC,IAAI,cAAc,GAAG,KAAK,CAAC,MAAM,CAAA;IACjC,KAAK,IAAI,CAAC,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACxC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACzB,cAAc,GAAG,CAAC,CAAA;YAClB,MAAK;QACP,CAAC;IACH,CAAC;IACD,IAAI,cAAc,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;QACpC,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAA;IACvC,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAS,qBAAqB,CAAC,GAAY;IACzC,OAAO,CACL,GAAG,KAAK,SAAS;QACjB,GAAG,KAAK,IAAI;QACZ,OAAO,GAAG,KAAK,QAAQ;QACvB,8CAA8C;QAC9C,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CACpD,CAAA;AACH,CAAC;AAED,SAAS,cAAc,CACrB,MAAuB,EACvB,MAAkB;IAElB,IAAI,WAAW,GAAG,EAAE,CAAA;IACpB,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;QACjC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/D,WAAW,GAAG,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;QACjD,CAAC;aAAM,IACL,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAChC,CAAC;YACD,WAAW,GAAG,eAAe,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;QACnE,CAAC;IACH,CAAC;IACD,OAAO,eAAe,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,WAAW,WAAW,MAAM,EAAE,CAAA;AAC5E,CAAC"}