{"name": "load-shp-file", "version": "1.0.0", "description": "Load SHP and DBF files and store them in ClickHouse database", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["shapefile", "dbf", "clickhouse", "gis", "geospatial"], "author": "", "license": "ISC", "type": "module", "dependencies": {"@clickhouse/client": "^1.12.0", "fs-extra": "^11.3.1", "glob": "^11.0.3", "path": "^0.12.7", "shapefile": "^0.6.6", "uuid": "^11.1.0"}}