# Shapefile 到 ClickHouse 加载器

这个 Node.js 项目用于读取 SHP 和 DBF 文件，并将数据存储到 ClickHouse 数据库中。

## 功能特性

- 🗺️ 支持读取 SHP 和 DBF 文件
- 🏗️ 自动创建合理的 ClickHouse 表结构
- 📊 分离存储几何数据和属性数据
- 🔄 批量处理大文件
- 📝 详细的处理日志和错误处理
- ⚙️ 灵活的配置选项

## 数据库表结构

项目会自动创建以下表：

### 1. 文件元数据表 (`file_metadata`)
- `id`: 文件唯一标识
- `file_name`: 文件名
- `file_path`: 文件路径
- `geometry_types`: 几何类型数组
- `feature_count`: 要素数量
- `attribute_schema`: 属性模式
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 2. 几何数据表 (`geometry_data`)
- `id`: 记录唯一标识
- `file_id`: 关联的文件ID
- `feature_id`: 要素ID
- `geometry_type`: 几何类型
- `geometry_wkt`: WKT 格式的几何数据
- `geometry_geojson`: GeoJSON 格式的几何数据
- `bbox_*`: 边界框坐标
- `created_at`: 创建时间

### 3. 属性数据表 (`attribute_data_*`)
- 为每个文件动态创建专门的属性表
- 包含文件的所有属性字段
- 自动推断字段类型

## 安装和使用

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# ClickHouse 数据库配置
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=your_password
CLICKHOUSE_DATABASE=gis_data

# SHP 文件配置
SHP_SOURCE_DIR=d:/tmp/shp_files/
BATCH_SIZE=1000

# 日志配置
LOG_LEVEL=info
```

### 3. 准备 SHP 文件

将 SHP 文件和对应的 DBF 文件放在配置的源目录中（默认：`d:/tmp/shp_files/`）。

### 4. 运行程序

```bash
# 生产模式
npm start

# 开发模式（自动重启）
npm run dev
```

## 项目结构

```
load-shp-file/
├── src/
│   ├── config/
│   │   └── config.js          # 配置文件
│   ├── lib/
│   │   ├── shapefileReader.js # SHP文件读取器
│   │   ├── clickhouseClient.js # ClickHouse客户端
│   │   └── dataProcessor.js   # 数据处理器
│   └── index.js               # 主程序入口
├── .env.example               # 环境变量示例
├── package.json
└── README.md
```

## 依赖包

- `shapefile`: SHP 文件读取
- `@clickhouse/client`: ClickHouse 客户端
- `fs-extra`: 文件系统操作
- `glob`: 文件模式匹配
- `uuid`: UUID 生成
- `path`: 路径处理

## 支持的几何类型

- Point（点）
- LineString（线）
- Polygon（面）
- MultiPoint（多点）
- MultiLineString（多线）
- MultiPolygon（多面）

## 错误处理

程序包含完善的错误处理机制：

- 文件不存在或无法读取时会跳过并记录错误
- 数据库连接失败时会终止程序
- 数据插入失败时会记录详细错误信息
- 程序结束时会自动清理资源

## 性能优化

- 批量插入数据（默认1000条/批）
- 为每个文件创建专门的属性表
- 使用 MergeTree 引擎优化查询性能
- 支持压缩传输

## 注意事项

1. 确保 ClickHouse 服务正在运行
2. 确保有足够的磁盘空间存储数据
3. 大文件处理可能需要较长时间
4. 建议在处理前备份重要数据

## 故障排除

### 连接 ClickHouse 失败
- 检查 ClickHouse 服务是否运行
- 验证连接配置是否正确
- 检查网络连接

### 文件读取失败
- 确认文件路径正确
- 检查文件权限
- 验证 SHP 和 DBF 文件完整性

### 内存不足
- 减少批处理大小
- 分批处理大文件
- 增加系统内存
