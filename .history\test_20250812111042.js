/**
 * 简单测试脚本
 * 用于验证项目功能
 */

import ShapefileReader from './src/lib/shapefileReader.js';
import config from './src/config/config.js';
import shapefile from 'shapefile';

async function testShapefileReader() {
  console.log('测试 Shapefile 读取功能...');

  try {
    const reader = new ShapefileReader(config.shp.sourceDirectory);

    // 获取文件列表
    const files = await reader.getShapefiles();
    console.log(`找到 ${files.length} 个 SHP 文件:`);
    files.forEach(file => console.log(`  - ${file}`));

    if (files.length > 0) {
      // 测试直接使用 shapefile 库
      const firstFile = files[0];
      console.log(`\n正在测试直接读取: ${firstFile}`);

      try {
        const dbfFile = firstFile.replace('.shp', '.dbf');
        console.log(`DBF 文件: ${dbfFile}`);

        // 使用 shapefile.read 方法
        const collection = await shapefile.read(firstFile, dbfFile);
        console.log(`成功读取 ${collection.features.length} 个要素`);

        if (collection.features.length > 0) {
          const firstFeature = collection.features[0];
          console.log('第一个要素:');
          console.log('  几何类型:', firstFeature.geometry?.type);
          console.log('  属性字段:', Object.keys(firstFeature.properties || {}));
        }
      } catch (directError) {
        console.error('直接读取失败:', directError);

        // 尝试使用我们的读取器
        console.log('\n尝试使用自定义读取器...');
        const data = await reader.readShapefile(firstFile);
        console.log(`文件名: ${data.fileName}`);
        console.log(`要素数量: ${data.featureCount}`);

        if (data.features.length > 0) {
          const analysis = reader.analyzeFeatures(data.features);
          console.log(`几何类型: ${analysis.geometryTypes.join(', ')}`);
          console.log(`属性字段:`, Object.keys(analysis.attributeSchema));
        }
      }
    }

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testShapefileReader();
