{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../../packages/client-node/src/config.ts"], "names": [], "mappings": ";;;AAKA,6DAIkC;AAIlC,6CAA+D;AAC/D,6CAAwC;AACxC,mCAA2C;AAmD9B,QAAA,cAAc,GAEvB;IACF,0BAA0B,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;QAC1C,MAAM,UAAU,GAAsC,EAAE,GAAG,MAAM,EAAE,CAAA;QACnE,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAA;QACvC,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAA;QACvC,MAAM,mBAAmB,GAAG,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAA;QACxD,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,mBAAmB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAClC,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAW,CAAA;gBACjD,QAAQ,GAAG,EAAE,CAAC;oBACZ,KAAK,4BAA4B;wBAC/B,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;4BACxC,UAAU,CAAC,UAAU,GAAG,EAAE,CAAA;wBAC5B,CAAC;wBACD,UAAU,CAAC,UAAU,CAAC,eAAe,GAAG,IAAA,oCAAoB,EAAC;4BAC3D,GAAG;4BACH,KAAK;4BACL,GAAG,EAAE,CAAC;yBACP,CAAC,CAAA;wBACF,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;wBACtB,MAAK;oBACP;wBACE,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;gBAC1B,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;QACD,OAAO;YACL,MAAM,EAAE,UAAU;YAClB,cAAc,EAAE,aAAa;YAC7B,cAAc,EAAE,aAAa;SAC9B,CAAA;IACH,CAAC;IACD,eAAe,EAAE,CACf,UAA6C,EAC7C,MAAwB,EACxB,EAAE;QACF,IAAI,GAAG,GAA0B,SAAS,CAAA;QAC1C,IAAI,UAAU,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;YACjC,IAAI,MAAM,IAAI,UAAU,CAAC,GAAG,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;gBACxD,GAAG,GAAG;oBACJ,IAAI,EAAE,QAAQ;oBACd,GAAG,UAAU,CAAC,GAAG;iBAClB,CAAA;YACH,CAAC;iBAAM,CAAC;gBACN,GAAG,GAAG;oBACJ,IAAI,EAAE,OAAO;oBACb,GAAG,UAAU,CAAC,GAAG;iBAClB,CAAA;YACH,CAAC;QACH,CAAC;QACD,iEAAiE;QACjE,MAAM,UAAU,GAAG;YACjB,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,IAAI,IAAI;YAChD,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,IAAI,IAAI;SACjE,CAAA;QACD,OAAO,IAAA,6BAAgB,EAAC;YACtB,iBAAiB,EAAE,MAAM;YACzB,qBAAqB,EAAE,UAAU,CAAC,qBAAqB,IAAI,IAAI;YAC/D,4BAA4B,EAC1B,UAAU,CAAC,4BAA4B,IAAI,KAAK;YAClD,UAAU,EAAE,UAAU,CAAC,UAAU;YACjC,UAAU;YACV,GAAG;SACJ,CAAC,CAAA;IACJ,CAAC;IACD,cAAc,EAAE,IAAI,yBAAiB,EAAE;IACvC,eAAe,EAAE,CAAC,CAChB,MAAuB,EACvB,MAAkB,EAClB,QAAgB,EAChB,SAA+B,EAC/B,gBAAiC,EACjC,EAAE,CACF,sBAAS,CAAC,QAAQ,CAAC;QACjB,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,gBAAgB;KACjB,CAAC,CAAQ;CACb,CAAA"}