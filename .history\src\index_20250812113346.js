/**
 * 主程序入口
 * 加载 SHP 和 DBF 文件并存储到 ClickHouse
 */

import ShapefileReader from './lib/shapefileReader.js';
import ClickHouseClient from './lib/clickhouseClient.js';
import DataProcessor from './lib/dataProcessor.js';
import config from './config/config.js';

class ShapefileLoader {
  constructor() {
    this.reader = new ShapefileReader(config.shp.sourceDirectory);
    this.client = new ClickHouseClient();
    this.processor = new DataProcessor();
  }

  /**
   * 初始化
   */
  async initialize() {
    console.log('正在初始化 Shapefile 加载器...');
    
    try {
      // 连接到 ClickHouse
      await this.client.connect();
      
      // 初始化数据库表
      await this.client.initializeTables();
      
      console.log('初始化完成');
    } catch (error) {
      console.error('初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理单个 shapefile
   */
  async processShapefile(shapefileData) {
    const { fileName } = shapefileData;
    console.log(`正在处理文件: ${fileName}`);

    try {
      // 处理数据
      const processedData = this.processor.processFeatures(shapefileData);
      const { metadata, geometryData, attributeData, attributeSchema } = processedData;

      // 插入元数据
      await this.client.insertBatch(config.tables.metadataTable, [metadata]);
      console.log(`元数据插入完成: ${fileName}`);

      // 插入几何数据
      if (geometryData.length > 0) {
        const geometryBatches = this.processor.batchData(geometryData, config.shp.batchSize);
        for (const batch of geometryBatches) {
          await this.client.insertBatch(config.tables.geometryTable, batch);
        }
        console.log(`几何数据插入完成: ${fileName} (${geometryData.length} 条记录)`);
      }

      // 创建并插入属性数据
      if (attributeData.length > 0 && Object.keys(attributeSchema).length > 0) {
        // 为每个文件创建专门的属性表
        const attributeTableName = `${config.tables.attributeTable}_${fileName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}`;
        await this.client.createAttributeTable(attributeSchema, attributeTableName);
        
        const attributeBatches = this.processor.batchData(attributeData, config.shp.batchSize);
        for (const batch of attributeBatches) {
          await this.client.insertBatch(attributeTableName, batch);
        }
        console.log(`属性数据插入完成: ${fileName} (${attributeData.length} 条记录)`);
      }

      console.log(`文件处理完成: ${fileName}`);
      return {
        success: true,
        fileName,
        featureCount: shapefileData.features.length,
        geometryCount: geometryData.length,
        attributeCount: attributeData.length
      };

    } catch (error) {
      console.error(`处理文件失败 ${fileName}:`, error);
      return {
        success: false,
        fileName,
        error: error.message
      };
    }
  }

  /**
   * 加载所有 shapefile
   */
  async loadAllShapefiles() {
    console.log('开始加载所有 Shapefile...');
    
    try {
      // 读取所有 shapefile
      const shapefileDataList = await this.reader.readAllShapefiles();
      
      if (shapefileDataList.length === 0) {
        console.log('未找到任何 Shapefile');
        return;
      }

      console.log(`找到 ${shapefileDataList.length} 个 Shapefile，开始处理...`);

      const results = [];
      
      // 逐个处理文件
      for (const shapefileData of shapefileDataList) {
        const result = await this.processShapefile(shapefileData);
        results.push(result);
      }

      // 输出处理结果统计
      this.printSummary(results);

    } catch (error) {
      console.error('加载 Shapefile 失败:', error);
      throw error;
    }
  }

  /**
   * 打印处理结果摘要
   */
  printSummary(results) {
    console.log('\n=== 处理结果摘要 ===');
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`总文件数: ${results.length}`);
    console.log(`成功处理: ${successful.length}`);
    console.log(`处理失败: ${failed.length}`);
    
    if (successful.length > 0) {
      const totalFeatures = successful.reduce((sum, r) => sum + (r.featureCount || 0), 0);
      const totalGeometry = successful.reduce((sum, r) => sum + (r.geometryCount || 0), 0);
      const totalAttributes = successful.reduce((sum, r) => sum + (r.attributeCount || 0), 0);
      
      console.log(`总要素数: ${totalFeatures}`);
      console.log(`总几何记录数: ${totalGeometry}`);
      console.log(`总属性记录数: ${totalAttributes}`);
    }
    
    if (failed.length > 0) {
      console.log('\n失败的文件:');
      failed.forEach(r => {
        console.log(`  - ${r.fileName}: ${r.error}`);
      });
    }
    
    console.log('===================\n');
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      await this.client.disconnect();
      console.log('资源清理完成');
    } catch (error) {
      console.error('清理资源失败:', error);
    }
  }

  /**
   * 运行主程序
   */
  async run() {
    try {
      await this.initialize();
      await this.loadAllShapefiles();
    } catch (error) {
      console.error('程序执行失败:', error);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// 主程序入口
async function main() {
  console.log('Shapefile 到 ClickHouse 加载器启动');
  console.log(`源目录: ${config.shp.sourceDirectory}`);
  console.log(`目标数据库: ${config.clickhouse.host}:${config.clickhouse.port}/${config.clickhouse.database}`);
  console.log('');

  const loader = new ShapefileLoader();
  await loader.run();
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 启动程序
if (import.meta.url.startsWith('file:') && process.argv[1] && import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/'))) {
  main().catch(console.error);
}

export default ShapefileLoader;
