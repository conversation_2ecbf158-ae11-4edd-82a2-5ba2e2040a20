/**
 * 配置文件
 */

export const config = {
  // ClickHouse 数据库配置
  clickhouse: {
    host: process.env.CLICKHOUSE_HOST || '************',
    port: process.env.CLICKHOUSE_PORT || 8123,
    username: process.env.CLICKHOUSE_USER || 'luzhen',
    password: process.env.CLICKHOUSE_PASSWORD || 'Itd^2320',
    database: process.env.CLICKHOUSE_DATABASE || 'default',
    // 连接选项
    options: {
      compression: {
        request: true,
        response: true,
      },
      max_open_connections: 10,
    }
  },

  // SOCKS 代理配置
  proxy: {
    enabled: process.env.PROXY_ENABLED !== 'false', // 默认启用代理
    host: process.env.PROXY_HOST || '*************',
    port: process.env.PROXY_PORT || 18080,
    username: process.env.PROXY_USER || 'luzhen',
    password: process.env.PROXY_PASSWORD || '4A!luzhen',
    type: 'socks5' // 支持 socks4, socks5, http
  },

  // SHP 文件配置
  shp: {
    // SHP 文件目录
    sourceDirectory: process.env.SHP_SOURCE_DIR || 'd:/tmp/shp_files/',
    // 支持的文件扩展名
    supportedExtensions: ['.shp', '.dbf'],
    // 批量处理大小
    batchSize: parseInt(process.env.BATCH_SIZE) || 1000,
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: true,
    enableFile: false,
  },

  // 表配置
  tables: {
    // 几何数据表名
    geometryTable: 'geometry_data',
    // 属性数据表名
    attributeTable: 'attribute_data',
    // 文件元数据表名
    metadataTable: 'file_metadata',
  }
};

export default config;
