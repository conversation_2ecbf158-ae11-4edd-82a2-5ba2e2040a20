/**
 * 简单测试脚本
 * 用于验证项目功能
 */

import ShapefileReader from './src/lib/shapefileReader.js';
import config from './src/config/config.js';

async function testShapefileReader() {
  console.log('测试 Shapefile 读取功能...');
  
  try {
    const reader = new ShapefileReader(config.shp.sourceDirectory);
    
    // 获取文件列表
    const files = await reader.getShapefiles();
    console.log(`找到 ${files.length} 个 SHP 文件:`);
    files.forEach(file => console.log(`  - ${file}`));
    
    if (files.length > 0) {
      // 读取第一个文件
      const firstFile = files[0];
      console.log(`\n正在读取第一个文件: ${firstFile}`);
      
      const data = await reader.readShapefile(firstFile);
      console.log(`文件名: ${data.fileName}`);
      console.log(`要素数量: ${data.featureCount}`);
      
      if (data.features.length > 0) {
        const analysis = reader.analyzeFeatures(data.features);
        console.log(`几何类型: ${analysis.geometryTypes.join(', ')}`);
        console.log(`属性字段:`, Object.keys(analysis.attributeSchema));
      }
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testShapefileReader();
