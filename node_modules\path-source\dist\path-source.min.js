// https://github.com/mbostock/path-source Version 0.1.3. Copyright 2017 <PERSON>.
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r(require("array-source")):"function"==typeof define&&define.amd?define(["array-source"],r):(e.sources=e.sources||{},e.sources.path=r(e.sources.array))}(this,function(e){"use strict";e=e&&e.hasOwnProperty("default")?e.default:e;var r=function(r){return fetch(r).then(function(r){return r.body&&r.body.getReader?r.body.getReader():r.arrayBuffer().then(e)})},n=function(r){return new Promise(function(n,t){var o=new XMLHttpRequest;o.responseType="arraybuffer",o.onload=function(){n(e(o.response))},o.onerror=t,o.ontimeout=t,o.open("GET",r,!0),o.send()})};return function(e){return("function"==typeof fetch?r:n)(e)}});