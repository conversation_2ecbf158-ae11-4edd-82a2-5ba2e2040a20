{"version": 3, "file": "node_custom_agent_connection.js", "sourceRoot": "", "sources": ["../../../../packages/client-node/src/connection/node_custom_agent_connection.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAuB;AACvB,kDAAyB;AAKzB,iEAA2D;AAC3D,6DAAkE;AAElE,MAAa,yBAA0B,SAAQ,yCAAkB;IAE/D,YAAY,MAA4B;QACtC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CACb,4DAA4D,CAC7D,CAAA;QACH,CAAC;QACD,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAA;QAPjB;;;;;WAAyD;QASxE,6DAA6D;QAC7D,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,aAAa,GAAG,eAAK,CAAC,OAAO,CAAA;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,cAAI,CAAC,OAAO,CAAA;QACnC,CAAC;IACH,CAAC;IAES,mBAAmB,CAAC,MAAqB;QACjD,MAAM,OAAO,GAAG,IAAA,sCAAsB,EAAC;YACrC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,0BAA0B,EAAE,MAAM,CAAC,0BAA0B;YAC7D,2BAA2B,EAAE,MAAM,CAAC,2BAA2B;SAChE,CAAC,CAAA;QACF,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE;YACpC,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe;YACpC,MAAM,EAAE,MAAM,CAAC,YAAY;YAC3B,OAAO;SACR,CAAC,CAAA;IACJ,CAAC;CACF;AAhCD,8DAgCC"}