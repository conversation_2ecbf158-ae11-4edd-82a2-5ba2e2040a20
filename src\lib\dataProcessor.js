/**
 * 数据处理模块
 */

import { v4 as uuidv4 } from 'uuid';

export class DataProcessor {
  constructor() {}

  /**
   * 将几何对象转换为 WKT 格式
   */
  geometryToWKT(geometry) {
    if (!geometry || !geometry.type) {
      return null;
    }

    try {
      switch (geometry.type) {
        case 'Point':
          return `POINT(${geometry.coordinates[0]} ${geometry.coordinates[1]})`;
        
        case 'LineString':
          const lineCoords = geometry.coordinates.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
          return `LINESTRING(${lineCoords})`;
        
        case 'Polygon':
          const rings = geometry.coordinates.map(ring => {
            const ringCoords = ring.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
            return `(${ringCoords})`;
          }).join(', ');
          return `POLYGON(${rings})`;
        
        case 'MultiPoint':
          const multiPointCoords = geometry.coordinates.map(coord => `(${coord[0]} ${coord[1]})`).join(', ');
          return `MULTIPOINT(${multiPointCoords})`;
        
        case 'MultiLineString':
          const multiLineCoords = geometry.coordinates.map(line => {
            const lineCoords = line.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
            return `(${lineCoords})`;
          }).join(', ');
          return `MULTILINESTRING(${multiLineCoords})`;
        
        case 'MultiPolygon':
          const multiPolygonCoords = geometry.coordinates.map(polygon => {
            const rings = polygon.map(ring => {
              const ringCoords = ring.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
              return `(${ringCoords})`;
            }).join(', ');
            return `(${rings})`;
          }).join(', ');
          return `MULTIPOLYGON(${multiPolygonCoords})`;
        
        default:
          console.warn(`不支持的几何类型: ${geometry.type}`);
          return null;
      }
    } catch (error) {
      console.error('几何对象转换为 WKT 失败:', error);
      return null;
    }
  }

  /**
   * 计算几何对象的边界框
   */
  calculateBoundingBox(geometry) {
    if (!geometry || !geometry.coordinates) {
      return { min_x: null, min_y: null, max_x: null, max_y: null };
    }

    let allCoords = [];

    // 递归提取所有坐标点
    const extractCoords = (coords) => {
      if (Array.isArray(coords[0])) {
        coords.forEach(extractCoords);
      } else {
        allCoords.push(coords);
      }
    };

    extractCoords(geometry.coordinates);

    if (allCoords.length === 0) {
      return { min_x: null, min_y: null, max_x: null, max_y: null };
    }

    const xs = allCoords.map(coord => coord[0]);
    const ys = allCoords.map(coord => coord[1]);

    return {
      min_x: Math.min(...xs),
      min_y: Math.min(...ys),
      max_x: Math.max(...xs),
      max_y: Math.max(...ys)
    };
  }

  /**
   * 处理要素数据，准备插入数据库
   */
  processFeatures(shapefileData) {
    const fileId = uuidv4();
    const { fileName, features, filePath } = shapefileData;

    // 处理元数据
    const metadata = {
      id: fileId,
      file_name: fileName,
      file_path: filePath,
      geometry_types: [...new Set(features.map(f => f.geometry?.type).filter(Boolean))],
      feature_count: features.length,
      attribute_schema: this.getAttributeSchema(features),
      created_at: new Date(),
      updated_at: new Date()
    };

    // 处理几何数据
    const geometryData = [];
    const attributeData = [];

    features.forEach((feature, index) => {
      const featureId = index + 1;

      // 处理几何数据
      if (feature.geometry) {
        const bbox = this.calculateBoundingBox(feature.geometry);
        const geometryRecord = {
          id: uuidv4(),
          file_id: fileId,
          feature_id: featureId,
          geometry_type: feature.geometry.type,
          geometry_wkt: this.geometryToWKT(feature.geometry),
          geometry_geojson: JSON.stringify(feature.geometry),
          bbox_min_x: bbox.min_x,
          bbox_min_y: bbox.min_y,
          bbox_max_x: bbox.max_x,
          bbox_max_y: bbox.max_y,
          created_at: new Date()
        };
        geometryData.push(geometryRecord);
      }

      // 处理属性数据
      if (feature.properties) {
        const attributeRecord = {
          id: uuidv4(),
          file_id: fileId,
          feature_id: featureId,
          created_at: new Date(),
          ...this.cleanProperties(feature.properties)
        };
        attributeData.push(attributeRecord);
      }
    });

    return {
      metadata,
      geometryData,
      attributeData,
      attributeSchema: this.getAttributeSchemaFromFeatures(features)
    };
  }

  /**
   * 获取属性模式
   */
  getAttributeSchema(features) {
    if (!features || features.length === 0) {
      return JSON.stringify({});
    }

    const schema = {};
    const sampleFeature = features[0];

    if (sampleFeature && sampleFeature.properties) {
      Object.keys(sampleFeature.properties).forEach(key => {
        const value = sampleFeature.properties[key];
        let type = 'String';
        
        if (typeof value === 'number') {
          type = Number.isInteger(value) ? 'Int64' : 'Float64';
        } else if (typeof value === 'boolean') {
          type = 'UInt8';
        } else if (value instanceof Date) {
          type = 'DateTime';
        }
        
        schema[key] = type;
      });
    }

    return JSON.stringify(schema);
  }

  /**
   * 获取属性模式对象
   */
  getAttributeSchemaFromFeatures(features) {
    if (!features || features.length === 0) {
      return {};
    }

    const schema = {};
    const sampleFeature = features[0];

    if (sampleFeature && sampleFeature.properties) {
      Object.keys(sampleFeature.properties).forEach(key => {
        const value = sampleFeature.properties[key];
        let type = 'String';
        
        if (typeof value === 'number') {
          type = Number.isInteger(value) ? 'Int64' : 'Float64';
        } else if (typeof value === 'boolean') {
          type = 'UInt8';
        } else if (value instanceof Date) {
          type = 'DateTime';
        }
        
        schema[key] = type;
      });
    }

    return schema;
  }

  /**
   * 清理属性数据，确保符合数据库要求
   */
  cleanProperties(properties) {
    const cleaned = {};
    
    Object.entries(properties).forEach(([key, value]) => {
      // 清理列名
      const cleanKey = key.replace(/[^a-zA-Z0-9_]/g, '_').toLowerCase();
      
      // 处理值
      if (value === null || value === undefined) {
        cleaned[cleanKey] = null;
      } else if (typeof value === 'string') {
        // 处理字符串，确保不会导致 SQL 注入
        cleaned[cleanKey] = value.trim();
      } else {
        cleaned[cleanKey] = value;
      }
    });

    return cleaned;
  }

  /**
   * 分批处理数据
   */
  batchData(data, batchSize = 1000) {
    const batches = [];
    for (let i = 0; i < data.length; i += batchSize) {
      batches.push(data.slice(i, i + batchSize));
    }
    return batches;
  }
}

export default DataProcessor;
