// https://github.com/mbostock/slice-source Version 0.4.1. Copyright 2016 <PERSON>.
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(e.sources=e.sources||{},e.sources.slice=r())}(this,function(){"use strict";function e(){return this._source.cancel()}function r(e,r){if(!e.length)return r;if(!r.length)return e;var n=new Uint8Array(e.length+r.length);return n.set(e),n.set(r,e.length),n}function n(){var e=this,n=e._array.subarray(e._index);return e._source.read().then(function(t){return e._array=u,e._index=0,t.done?n.length>0?{done:!1,value:n}:{done:!0,value:void 0}:{done:!1,value:r(n,t.value)}})}function t(e){if((e|=0)<0)throw new Error("invalid length");var r=this,n=this._array.length-this._index;if(this._index+e<=this._array.length)return Promise.resolve(this._array.subarray(this._index,this._index+=e));var t=new Uint8Array(e);return t.set(this._array.subarray(this._index)),function i(){return r._source.read().then(function(a){return a.done?(r._array=u,r._index=0,n>0?t.subarray(0,n):null):n+a.value.length>=e?(r._array=a.value,r._index=e-n,t.set(a.value.subarray(0,e-n),n),t):(t.set(a.value,n),n+=a.value.length,i())})}()}function i(e){return"function"==typeof e.slice?e:new a("function"==typeof e.read?e:e.getReader())}function a(e){this._source=e,this._array=u,this._index=0}var u=new Uint8Array(0);return a.prototype.read=n,a.prototype.slice=t,a.prototype.cancel=e,i});