{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../../../packages/client-common/src/error/error.ts"], "names": [], "mappings": ";;;AAwBA,gCAUC;AAID,oDAUC;AAKD,8CAWC;AAhED,MAAM,OAAO,GACX,8FAA8F,CAAA;AAQhG,wDAAwD;AACxD,MAAa,eAAgB,SAAQ,KAAK;IAGxC,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAyB;QACxD,KAAK,CAAC,OAAO,CAAC,CAAA;QAHP;;;;;WAAY;QACZ;;;;;WAAwB;QAG/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAEhB,qCAAqC;QACrC,gIAAgI;QAChI,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,SAAS,CAAC,CAAA;IACxD,CAAC;CACF;AAZD,0CAYC;AAED,SAAgB,UAAU,CAAC,KAAqB;IAC9C,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAA;IAC3C,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAA;IACpD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IACpC,MAAM,MAAM,GAAG,KAAK,EAAE,MAA2C,CAAA;IACjE,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,IAAI,eAAe,CAAC,MAAM,CAAC,CAAA;IACpC,CAAC;SAAM,CAAC;QACN,OAAO,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAA;IAChD,CAAC;AACH,CAAC;AAED;sGACsG;AACtG,SAAgB,oBAAoB;IAClC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK,CAAA;IAC/B,IAAI,CAAC,KAAK;QAAE,OAAO,EAAE,CAAA;IAErB,gFAAgF;IAChF,iBAAiB;IACjB,kCAAkC;IAClC,qEAAqE;IACrE,gEAAgE;IAChE,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACxD,CAAC;AAED;;oFAEoF;AACpF,SAAgB,iBAAiB,CAC/B,GAAM,EACN,UAA8B;IAE9B,IAAI,GAAG,CAAC,KAAK,IAAI,UAAU,EAAE,CAAC;QAC5B,MAAM,iBAAiB,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACjD,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAA;QAC3D,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAA;QAC3D,GAAG,CAAC,KAAK,GAAG,GAAG,SAAS,KAAK,UAAU,KAAK,QAAQ,EAAE,CAAA;IACxD,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC"}