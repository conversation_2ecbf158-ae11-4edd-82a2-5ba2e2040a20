/**
 * 测试 ClickHouse 连接（通过 SOCKS 代理）
 */

import ClickHouseClient from './src/lib/clickhouseClient.js';
import config from './src/config/config.js';

async function testConnection() {
  console.log('测试 ClickHouse 连接...');
  console.log(`目标服务器: ${config.clickhouse.host}:${config.clickhouse.port}`);
  console.log(`数据库: ${config.clickhouse.database}`);
  console.log(`用户名: ${config.clickhouse.username}`);
  
  if (config.proxy.enabled) {
    console.log(`SOCKS 代理: ${config.proxy.host}:${config.proxy.port}`);
    console.log(`代理用户: ${config.proxy.username}`);
  } else {
    console.log('未启用代理');
  }
  
  const client = new ClickHouseClient();
  
  try {
    // 测试连接
    console.log('\n正在连接...');
    await client.connect();
    
    // 测试查询
    console.log('正在执行测试查询...');
    const result = await client.query('SELECT version()');
    const data = await result.json();
    console.log('ClickHouse 版本:', data.data[0]['version()']);
    
    // 测试数据库操作
    console.log('正在测试数据库操作...');
    await client.createDatabase();
    
    console.log('✅ 连接测试成功！');
    
  } catch (error) {
    console.error('❌ 连接测试失败:', error.message);
    if (error.code) {
      console.error('错误代码:', error.code);
    }
    if (error.response) {
      console.error('响应状态:', error.response.status);
    }
  } finally {
    await client.disconnect();
  }
}

// 运行测试
testConnection();
