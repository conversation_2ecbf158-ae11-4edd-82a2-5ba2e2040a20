{"version": 3, "file": "formatter.js", "sourceRoot": "", "sources": ["../../../../packages/client-common/src/data_formatter/formatter.ts"], "names": [], "mappings": ";;;AA4EA,8DAOC;AAED,wDAIC;AAED,oDAEC;AAED,oDAWC;AAQD,gCAOC;AAzHY,QAAA,qBAAqB,GAAG;IACnC,aAAa;IACb,oBAAoB;IACpB,oBAAoB;IACpB,2BAA2B;IAC3B,6BAA6B;IAC7B,qCAAqC;IACrC,oCAAoC;IACpC,4CAA4C;IAC5C,yBAAyB;CACjB,CAAA;AACG,QAAA,kBAAkB,GAAG,CAAC,mBAAmB,CAAU,CAAA;AACnD,QAAA,yBAAyB,GAAG;IACvC,MAAM;IACN,aAAa;IACb,aAAa;IACb,oBAAoB;IACpB,yBAAyB;CACjB,CAAA;AACG,QAAA,oBAAoB,GAAG;IAClC,GAAG,0BAAkB;IACrB,GAAG,iCAAyB;IAC5B,GAAG,6BAAqB;CAChB,CAAA;AACG,QAAA,mBAAmB,GAAG;IACjC,KAAK;IACL,cAAc;IACd,sBAAsB;IACtB,cAAc;IACd,iBAAiB;IACjB,uBAAuB;IACvB,+BAA+B;IAC/B,iBAAiB;IACjB,0BAA0B;IAC1B,kCAAkC;IAClC,SAAS;CACD,CAAA;AACG,QAAA,iBAAiB,GAAG;IAC/B,GAAG,6BAAqB;IACxB,GAAG,2BAAmB;CACd,CAAA;AAoCV,SAAgB,yBAAyB,CACvC,MAAkB;IAElB,OAAO,CACJ,iCAA+C,CAAC,QAAQ,CAAC,MAAM,CAAC;QAChE,0BAAwC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAC3D,CAAA;AACH,CAAC;AAED,SAAgB,sBAAsB,CACpC,MAAkB;IAElB,OAAQ,6BAA2C,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;AACtE,CAAC;AAED,SAAgB,oBAAoB,CAAC,UAAsB;IACzD,OAAQ,2BAAyC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;AACxE,CAAC;AAED,SAAgB,oBAAoB,CAClC,MAAW;IAEX,IAAI,CAAC,yBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,KAAK,CACb,GAAG,MAAM,kDAAkD,yBAAiB,CAAC,IAAI,CAC/E,GAAG,CACJ,EAAE,CACJ,CAAA;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED;;;;;GAKG;AACH,SAAgB,UAAU,CAAC,KAAU,EAAE,MAAkB;IACvD,IAAK,4BAA0C,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACjE,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;IACrC,CAAC;IACD,MAAM,IAAI,KAAK,CACb,iDAAiD,MAAM,WAAW,CACnE,CAAA;AACH,CAAC"}